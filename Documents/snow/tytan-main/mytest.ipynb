import numpy as np
from tytan import symbols, Compile, clear_symbol_registry
from tytan.qaia_sampler import QAIASampler


if True:
    """Test QAIASampler with traditional binary symbols."""
    print("=== Testing Binary Symbols (QUBO) ===")
    
    clear_symbol_registry()
    
    # Create binary symbols (default behavior)
    x1, x2 = symbols('x1 x2')  # symbol_type="binary" is default
    print(f"Created binary symbols: x1, x2")
    
    # Simple constraint: exactly one should be 1
    expr = (x1 + x2 - 1)**2
    print(f"Expression: (x1 + x2 - 1)^2")
    
    # Compile to QUBO
    qubo, offset = Compile(expr).get_qubo()
    matrix, index_map = qubo
    
    print(f"QUBO matrix:\n{matrix}")
    print(f"Variables: {list(index_map.keys())}")
    print(f"Offset: {offset}")
    
    # Test with different QAIA algorithms
    algorithms = ['asb', 'bsb', 'dsb']
    
    for algorithm in algorithms:
        print(f"\n--- Testing {algorithm.upper()} with binary symbols ---")
        try:
            sampler = QAIASampler(algorithm=algorithm, seed=42, backend='cpu-float32')
            result = sampler.run(qubo, shots=10, n_iter=50, dt=0.1, xi=0.1)
            
            best_solution = result[0][0]
            best_energy = result[0][1]
            
            print(f"Best solution: {best_solution}")
            print(f"Best energy: {best_energy}")
            
            # Verify constraint: x1 + x2 should equal 1
            constraint_value = best_solution['x1'] + best_solution['x2']
            constraint_satisfied = (constraint_value == 1)
            print(f"Constraint (x1 + x2 = 1): {constraint_value} {'✓' if constraint_satisfied else '✗'}")
            
            # Verify binary values
            for var, val in best_solution.items():
                if val not in [0, 1]:
                    print(f"❌ Non-binary value found: {var} = {val}")
                    # return False
            
            print(f"✅ {algorithm.upper()} with binary symbols: SUCCESS")
            
        except Exception as e:
            print(f"❌ {algorithm.upper()} with binary symbols: FAILED - {e}")
            # return False

from tytan import symbols, Compile, clear_symbol_registry
from tytan.qaia_sampler import QAIASampler


if True:
    """Basic example with Ising symbols."""
    print("=== Basic Ising Symbols Example ===")
    
    clear_symbol_registry()
    
    # Create Ising symbols as requested
    s1, s2 = symbols('s1 s2', symbol_type="ising")
    print(f"Created Ising symbols: s1, s2 (values ∈ {{-1, +1}})")
    
    # Ising constraint: s1 should equal s2
    expr = (s1 - s2)**2
    print(f"Ising expression: (s1 - s2)^2")
    print("Goal: minimize energy when s1 = s2 (both +1 or both -1)")
    
    # Compile to QUBO (automatic Ising→QUBO conversion)
    qubo, offset = Compile(expr).get_qubo()
    matrix, index_map = qubo
    
    print(f"\nAfter automatic Ising→QUBO conversion:")
    print(f"QUBO matrix:\n{matrix}")
    print(f"Variables: {list(index_map.keys())}")
    print(f"Offset: {offset}")
    
    # Solve with QAIASampler
    sampler = QAIASampler(algorithm='asb', seed=42, backend='cpu-float32')
    result = sampler.run(qubo, shots=20, n_iter=100, dt=0.1, xi=0.1)
    
    print(f"\nQAIASampler results:")
    print(f"Found {len(result)} unique solutions")
    
    # Show best solution
    best_solution = result[0][0]
    best_energy = result[0][1]
    count = result[0][2]
    
    print(f"\nBest solution (binary format): {best_solution}")
    print(f"Energy: {best_energy}")
    print(f"Count: {count}")
    
    # Convert back to Ising interpretation
    s1_ising = 2 * best_solution['s1_bin'] - 1  # 0→-1, 1→+1
    s2_ising = 2 * best_solution['s2_bin'] - 1
    
    print(f"\nIsing interpretation:")
    print(f"s1 = {s1_ising}, s2 = {s2_ising}")
    print(f"Constraint satisfied (s1 = s2): {'✓' if s1_ising == s2_ising else '✗'}")

from tytan import *

#量子ビットを用意
x = symbols('x')
y = symbols('y')
z = symbols('z')

#式を記述（3個のうち2個だけ1にする）
H = (x + y + z - 2)**2

#コンパイル
qubo, offset = Compile(H).get_qubo()

#サンプラー選択
solver = sampler.QAIASampler(algorithm='bsb', seed=42, backend='cpu-float32')

#サンプリング
result = solver.run(qubo)

#結果
for r in result:
    print(r)



from tytan import symbols, Compile, clear_symbol_registry
from tytan.qaia_sampler import QAIASampler


def main():
    print("🔧 FINAL BUG FIX VERIFICATION")
    print("="*50)
    
    clear_symbol_registry()
    x, y, z = symbols('x y z')
    H = (x + y + z - 2)**2
    
    print("Problem: H = (x + y + z - 2)^2")
    print("Find x, y, z ∈ {0,1} such that exactly 2 variables equal 1")
    print("Expected optimal solutions:")
    print("  - {x:1, y:1, z:0} with energy -4")
    print("  - {x:1, y:0, z:1} with energy -4") 
    print("  - {x:0, y:1, z:1} with energy -4")
    
    qubo, offset = Compile(H).get_qubo()
    
    print(f"\nQUBO offset: {offset}")
    
    # Test the working algorithm with multiple runs
    print("\n🧪 Testing DSB algorithm (the one that worked):")
    
    all_optimal_solutions = set()
    total_runs = 5
    
    for run in range(total_runs):
        print(f"\nRun {run + 1}/{total_runs}:")
        
        try:
            sampler = QAIASampler(algorithm='asb', seed=42+run, backend='cpu-float32')
            result = sampler.run(qubo, shots=1000, n_iter=1000, dt=0.01, xi=0.01)
            
            optimal_found = []
            for solution, energy, count in result:
                if energy == -4.0:  # Optimal energy
                    sol_tuple = tuple(sorted(solution.items()))
                    all_optimal_solutions.add(sol_tuple)
                    optimal_found.append((solution, count))
            
            if optimal_found:
                print(f"  ✅ Found {len(optimal_found)} optimal solution(s):")
                for sol, count in optimal_found:
                    print(f"    {sol} (count: {count})")
            else:
                print(f"  ❌ No optimal solutions found")
                
        except Exception as e:
            print(f"  ❌ Error: {e}")
    
    print(f"\n📊 SUMMARY ACROSS ALL RUNS:")
    print(f"Total unique optimal solutions found: {len(all_optimal_solutions)}")
    
    if all_optimal_solutions:
        print("Optimal solutions discovered:")
        for sol_tuple in sorted(all_optimal_solutions):
            sol_dict = dict(sol_tuple)
            print(f"  {sol_dict}")
        
        expected_solutions = {
            (('x', 1), ('y', 1), ('z', 0)),
            (('x', 1), ('y', 0), ('z', 1)),
            (('x', 0), ('y', 1), ('z', 1))
        }
        
        found_expected = all_optimal_solutions.intersection(expected_solutions)
        
        print(f"\n🎯 VERIFICATION:")
        print(f"Expected solutions found: {len(found_expected)}/3")
        
        if len(found_expected) > 0:
            print("✅ BUG SUCCESSFULLY FIXED!")
            print("✅ QAIASampler can now find optimal solutions")
            print("✅ QUBO to Ising conversion is working correctly")
        else:
            print("⚠️  Bug partially fixed - algorithm works but may need parameter tuning")
    else:
        print("❌ Bug not fully fixed - no optimal solutions found across all runs")
    
    print("\n" + "="*50)
    print("🏁 TEST COMPLETE")

main()

