import numpy as np
from typing import List, Tuple, Dict, Any, Optional, Union
import warnings

try:
    import torch
    _TORCH_AVAILABLE = True
except ImportError:
    _TORCH_AVAILABLE = False

from .qaia_algorithm import ASB, BSB, DSB
from .sampler import get_result


class QAIASampler:
    """
    QAIA (Quantum Annealing Inspired Algorithm) Sampler for QUBO problems.

    This sampler integrates the QAIA algorithms (ASB, BSB, DSB) and provides automatic
    transformation from QUBO format to Ising model format. The sampler applies torch.sign()
    to the raw output to properly read out Ising model solutions and converts them back
    to binary format for consistency with the existing codebase.

    Args:
        algorithm (str): QAIA algorithm to use. Options: 'asb', 'bsb', 'dsb'. Default: 'asb'.
        seed (Optional[int]): Random seed for reproducibility. Default: None.
        backend (str): Computation backend. Options: 'cpu-float32', 'gpu-float32', 'npu-float32'.
                      Default: 'cpu-float32'.
        **kwargs: Additional parameters passed to the QAIA algorithm.

    Examples:
        >>> from tytan import symbols, Compile
        >>> from tytan.qaia_sampler import QAIASampler
        >>>
        >>> # Create a simple QUBO problem
        >>> x, y = symbols('x y')
        >>> expr = (x + y - 1)**2
        >>> qubo, offset = Compile(expr).get_qubo()
        >>>
        >>> # Solve using QAIA
        >>> sampler = QAIASampler(algorithm='asb', backend='cpu-float32')
        >>> result = sampler.run(qubo, shots=100)
        >>> print(result[0])  # Best solution
    """

    def __init__(
        self,
        algorithm: str = 'asb',
        seed: Optional[int] = None,
        backend: str = 'cpu-float32',
        **kwargs
    ):
        """Initialize the QAIASampler."""
        # Validate algorithm choice
        valid_algorithms = {'asb', 'bsb', 'dsb'}
        if algorithm.lower() not in valid_algorithms:
            raise ValueError(f"algorithm must be one of {valid_algorithms}, got '{algorithm}'")

        self.algorithm = algorithm.lower()
        self.seed = seed
        self.backend = backend
        self.kwargs = kwargs

        # Check torch availability for GPU backends
        if backend.startswith('gpu') and not _TORCH_AVAILABLE:
            raise ImportError("PyTorch is required for GPU backends. Please install PyTorch.")

    def _qubo_to_ising(self, qubo_matrix: np.ndarray, index_map: Dict[str, int]) -> Tuple[np.ndarray, np.ndarray, Dict[str, int], float]:
        """
        Convert QUBO format to Ising format.

        QUBO: minimize x^T Q x where x ∈ {0,1}^n
        Ising: minimize s^T J s + h^T s + constant where s ∈ {-1,+1}^n

        Transformation: x = (s + 1)/2, so s = 2x - 1

        Args:
            qubo_matrix (np.ndarray): QUBO matrix Q
            index_map (Dict[str, int]): Mapping from variable names to indices

        Returns:
            Tuple[np.ndarray, np.ndarray, Dict[str, int], float]:
                (J_matrix, h_vector, ising_index_map, offset)
        """
        Q = qubo_matrix.copy()
        n = Q.shape[0]

        # Initialize Ising parameters
        J = np.zeros((n, n))
        h = np.zeros(n)
        offset = 0.0

        # Standard QUBO to Ising conversion using the correct mathematical transformation
        # QUBO: minimize x^T Q x where x ∈ {0,1}^n
        # Ising: minimize s^T J s + h^T s + constant where s ∈ {-1,+1}^n
        # Transformation: x_i = (s_i + 1)/2

        # Handle upper triangular QUBO matrix correctly
        # The QUBO matrix from TYTAN is upper triangular where Q[i,j] for i<j
        # already contains the full coefficient for the x_i*x_j term

        # J_ij = Q_ij / 4 for i < j (upper triangular)
        for i in range(n):
            for j in range(i+1, n):
                if Q[i, j] != 0:
                    J[i, j] = Q[i, j] / 4
                    J[j, i] = Q[i, j] / 4  # Make symmetric

        # h_i = Q_ii / 2 + sum_{j≠i} Q_ij / 4 (considering upper triangular structure)
        # First set diagonal contributions
        for i in range(n):
            h[i] = Q[i, i] / 2

        # Then add off-diagonal contributions (avoid double counting)
        for i in range(n):
            for j in range(i+1, n):
                if Q[i, j] != 0:  # Upper triangular
                    h[i] += Q[i, j] / 4  # Contribution to h[i]
                    h[j] += Q[i, j] / 4  # Contribution to h[j]

        # constant = sum_i Q_ii / 4 + sum_{i<j} Q_ij / 4
        offset = 0.0
        for i in range(n):
            offset += Q[i, i] / 4  # Diagonal terms
        for i in range(n):
            for j in range(i+1, n):
                if Q[i, j] != 0:
                    offset += Q[i, j] / 4  # Upper triangular off-diagonal terms

        # Create Ising index map (same as QUBO for variable names)
        ising_index_map = index_map.copy()

        return J, h, ising_index_map, offset

    def _ising_to_binary_solution(self, ising_solution: np.ndarray) -> np.ndarray:
        """
        Convert Ising solution back to binary solution.

        Args:
            ising_solution (np.ndarray): Ising solution with values in {-1, +1}

        Returns:
            np.ndarray: Binary solution with values in {0, 1}
        """
        # Transform s ∈ {-1, +1} to x ∈ {0, 1} using x = (s + 1)/2
        return ((ising_solution + 1) / 2).astype(int)

    def run(
        self,
        qubomix: Tuple[np.ndarray, Dict[str, int]],
        shots: int = 100,
        n_iter: int = 1000,
        **run_kwargs
    ) -> List[List[Any]]:
        """
        Run the QAIA sampler on a QUBO problem.

        Args:
            qubomix (Tuple[np.ndarray, Dict[str, int]]): QUBO matrix and index mapping
            shots (int): Number of samples to generate. Default: 100.
            n_iter (int): Number of iterations for the QAIA algorithm. Default: 1000.
            **run_kwargs: Additional parameters for the QAIA algorithm.

        Returns:
            List[List[Any]]: Results in the format expected by get_result():
                [[{var: value}, energy, count], ...] sorted by energy (lowest first)
        """
        # Unpack QUBO input
        qubo_matrix, index_map = qubomix

        # Validate inputs
        if not isinstance(qubo_matrix, np.ndarray):
            raise TypeError("QUBO matrix must be a numpy array")
        if qubo_matrix.shape[0] != qubo_matrix.shape[1]:
            raise ValueError("QUBO matrix must be square")
        if len(index_map) != qubo_matrix.shape[0]:
            raise ValueError("Index map size must match QUBO matrix dimension")

        # Convert QUBO to Ising format
        J, h, ising_index_map, offset = self._qubo_to_ising(qubo_matrix, index_map)

        # Set random seed if provided
        if self.seed is not None:
            np.random.seed(self.seed)
            if _TORCH_AVAILABLE:
                torch.manual_seed(self.seed)

        # Merge run_kwargs with initialization kwargs
        algorithm_kwargs = {**self.kwargs, **run_kwargs}
        algorithm_kwargs.pop('shots', None)  # Remove shots from algorithm kwargs
        algorithm_kwargs.pop('n_iter', None)  # Remove n_iter from algorithm kwargs

        # Initialize the selected QAIA algorithm
        try:
            if self.algorithm == 'asb':
                solver = ASB(
                    J=J,
                    h=h,
                    batch_size=shots,
                    n_iter=n_iter,
                    backend=self.backend,
                    **algorithm_kwargs
                )
            elif self.algorithm == 'bsb':
                solver = BSB(
                    J=J,
                    h=h,
                    batch_size=shots,
                    n_iter=n_iter,
                    backend=self.backend,
                    **algorithm_kwargs
                )
            elif self.algorithm == 'dsb':
                solver = DSB(
                    J=J,
                    h=h,
                    batch_size=shots,
                    n_iter=n_iter,
                    backend=self.backend,
                    **algorithm_kwargs
                )
            else:
                raise ValueError(f"Unknown algorithm: {self.algorithm}")

        except Exception as e:
            raise RuntimeError(f"Failed to initialize {self.algorithm.upper()} algorithm: {e}")

        # Run the QAIA algorithm
        try:
            solver.update()
        except Exception as e:
            raise RuntimeError(f"QAIA algorithm execution failed: {e}")

        # Get the raw solutions and apply sign function
        if self.backend.startswith('gpu') or self.backend.startswith('npu'):
            # For GPU/NPU backends, convert to CPU first
            raw_solutions = solver.x.cpu().detach().numpy() if hasattr(solver.x, 'cpu') else solver.x
        else:
            raw_solutions = solver.x

        # Apply torch.sign() equivalent to get discrete Ising solutions
        ising_solutions = np.sign(raw_solutions)  # Shape: (n_variables, batch_size)
        ising_solutions = ising_solutions.T  # Shape: (batch_size, n_variables)

        # Convert Ising solutions back to binary format
        binary_solutions = self._ising_to_binary_solution(ising_solutions)

        # Calculate energies for the binary solutions using original QUBO
        energies = []
        for sol in binary_solutions:
            energy = sol.T @ qubo_matrix @ sol
            energies.append(energy)
        energies = np.array(energies)

        # Use the standard get_result function to format output
        result = get_result(binary_solutions, energies, index_map)

        return result
