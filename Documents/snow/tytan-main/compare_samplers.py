#!/usr/bin/env python3
"""
Compare QAIASampler with other samplers to see if the issue is with the conversion or the algorithm.
"""

import sys
sys.path.insert(0, '/Users/<USER>/Documents/snow/tytan-main')

from tytan import symbols, Compile, clear_symbol_registry
from tytan.qaia_sampler import QAIASampler
from tytan.sampler import GASampler


def main():
    print("Comparing QAIASampler with GASampler")
    
    clear_symbol_registry()
    x, y, z = symbols('x y z')
    H = (x + y + z - 2)**2
    
    print("Problem: H = (x + y + z - 2)^2")
    print("Expected optimal solutions: [1,1,0], [1,0,1], [0,1,1] with energy -4")
    
    qubo, offset = Compile(H).get_qubo()
    
    print(f"\nQUBO offset: {offset}")
    
    # Test GASampler first
    print("\n=== Testing GASampler ===")
    try:
        ga_sampler = GASampler(seed=42)
        ga_result = ga_sampler.run(qubo, shots=100, verbose=False)
        
        print(f"GASampler found {len(ga_result)} unique solutions:")
        for i, (solution, energy, count) in enumerate(ga_result[:5]):
            total = sum(solution.values())
            is_optimal = (energy == -4.0)
            print(f"  Solution {i+1}: {solution}")
            print(f"    Energy: {energy}, Count: {count}, Sum: {total}, Optimal: {'✓' if is_optimal else '✗'}")
            
    except Exception as e:
        print(f"GASampler error: {e}")
    
    # Test QAIASampler
    print("\n=== Testing QAIASampler ===")
    try:
        qaia_sampler = QAIASampler(algorithm='asb', seed=42, backend='cpu-float32')
        qaia_result = qaia_sampler.run(qubo, shots=50, n_iter=500, dt=0.1, xi=0.1)
        
        print(f"QAIASampler found {len(qaia_result)} unique solutions:")
        for i, (solution, energy, count) in enumerate(qaia_result[:5]):
            total = sum(solution.values())
            is_optimal = (energy == -4.0)
            print(f"  Solution {i+1}: {solution}")
            print(f"    Energy: {energy}, Count: {count}, Sum: {total}, Optimal: {'✓' if is_optimal else '✗'}")
            
    except Exception as e:
        print(f"QAIASampler error: {e}")


if __name__ == "__main__":
    main()
